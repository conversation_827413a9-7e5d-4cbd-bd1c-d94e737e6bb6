import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  Card,
  CardContent,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import {
  History as HistoryIcon,
  Compare as CompareIcon,
  Restore as RestoreIcon,
  MoreVert as MoreVertIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Edit as EditIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Remove as EqualIcon,
} from '@mui/icons-material';
import type { RationHistory, RationVersion, RationDiff } from '../../types';
import { rationHistoryService } from '../../services/rationHistoryService';

interface HistoryViewerProps {
  rationId: string;
  onVersionRestore?: (version: RationVersion) => void;
}

const HistoryViewer: React.FC<HistoryViewerProps> = ({
  rationId,
  onVersionRestore,
}) => {
  const [history, setHistory] = useState<RationHistory | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [compareDialogOpen, setCompareDialogOpen] = useState(false);
  const [selectedVersions, setSelectedVersions] = useState<string[]>([]);
  const [comparisonResult, setComparisonResult] = useState<RationDiff | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedVersion, setSelectedVersion] = useState<RationVersion | null>(null);

  useEffect(() => {
    loadHistory();
  }, [rationId]);

  const loadHistory = async () => {
    try {
      setLoading(true);
      const historyData = await rationHistoryService.getRationHistory(rationId);
      setHistory(historyData);
      setError(null);
    } catch (err) {
      setError('Failed to load ration history');
      console.error('Error loading history:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleVersionSelect = (versionId: string) => {
    setSelectedVersions(prev => {
      if (prev.includes(versionId)) {
        return prev.filter(id => id !== versionId);
      } else if (prev.length < 2) {
        return [...prev, versionId];
      } else {
        return [prev[1], versionId]; // Replace first selection
      }
    });
  };

  const handleCompareVersions = async () => {
    if (selectedVersions.length !== 2) return;

    try {
      const diff = await rationHistoryService.compareVersions(
        selectedVersions[0],
        selectedVersions[1]
      );
      setComparisonResult(diff);
      setCompareDialogOpen(true);
    } catch (err) {
      setError('Failed to compare versions');
      console.error('Error comparing versions:', err);
    }
  };

  const handleRestoreVersion = async (version: RationVersion) => {
    try {
      const restoredVersion = await rationHistoryService.restoreToVersion(
        rationId,
        version.id
      );
      
      if (onVersionRestore) {
        onVersionRestore(restoredVersion);
      }
      
      // Reload history to show the new version
      loadHistory();
    } catch (err) {
      setError('Failed to restore version');
      console.error('Error restoring version:', err);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, version: RationVersion) => {
    setAnchorEl(event.currentTarget);
    setSelectedVersion(version);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedVersion(null);
  };

  const getChangeIcon = (changeType: RationVersion['changeType']) => {
    switch (changeType) {
      case 'created':
        return <AddIcon color="success" />;
      case 'updated':
        return <EditIcon color="primary" />;
      case 'optimized':
        return <TrendingUpIcon color="warning" />;
      case 'restored':
        return <RestoreIcon color="info" />;
      default:
        return <HistoryIcon />;
    }
  };

  const getChangeColor = (changeType: RationVersion['changeType']) => {
    switch (changeType) {
      case 'created':
        return 'success';
      case 'updated':
        return 'primary';
      case 'optimized':
        return 'warning';
      case 'restored':
        return 'info';
      default:
        return 'default';
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getChangeTypeIcon = (changeType: string) => {
    switch (changeType) {
      case 'added':
        return <AddIcon color="success" fontSize="small" />;
      case 'removed':
        return <RemoveIcon color="error" fontSize="small" />;
      case 'modified':
        return <EditIcon color="warning" fontSize="small" />;
      default:
        return <EqualIcon color="action" fontSize="small" />;
    }
  };

  if (loading) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography>Loading history...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!history || history.versions.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="h6" color="text.secondary">
          No version history available
        </Typography>
        <Typography color="text.secondary">
          Changes to this ration will appear here
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Ration History ({history.totalVersions} versions)
        </Typography>
        <Box>
          {selectedVersions.length === 2 && (
            <Button
              variant="outlined"
              startIcon={<CompareIcon />}
              onClick={handleCompareVersions}
              sx={{ mr: 1 }}
            >
              Compare Selected
            </Button>
          )}
          <Typography variant="caption" color="text.secondary">
            Select 2 versions to compare
          </Typography>
        </Box>
      </Box>

      <Box>
        {history.versions.map((version, index) => (
          <Paper
            key={version.id}
            sx={{
              p: 2,
              mb: 2,
              border: selectedVersions.includes(version.id) ? 2 : 1,
              borderColor: selectedVersions.includes(version.id) ? 'primary.main' : 'divider',
              cursor: 'pointer',
              position: 'relative',
            }}
            onClick={() => handleVersionSelect(version.id)}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mr: 2 }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  bgcolor: `${getChangeColor(version.changeType)}.light`,
                  color: `${getChangeColor(version.changeType)}.main`
                }}>
                  {getChangeIcon(version.changeType)}
                </Box>
                <Box sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Typography variant="h6">
                      Version {version.version}
                    </Typography>
                    <Chip
                      label={version.changeType}
                      size="small"
                      color={getChangeColor(version.changeType) as any}
                    />
                    {version.id === history.versions[0].id && (
                      <Chip label="Current" size="small" color="success" />
                    )}
                  </Box>

                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {version.changeDescription}
                  </Typography>

                  <Typography variant="caption" color="text.secondary">
                    {formatDate(version.createdAt)} by {version.changedBy}
                  </Typography>

                  <Grid container spacing={2} sx={{ mt: 1 }}>
                    <Grid size={{ xs: 6, sm: 3 }}>
                      <Typography variant="caption" color="text.secondary">
                        Cost: ₹{(version.totalCost / version.totalWeight).toFixed(2)}/kg
                      </Typography>
                    </Grid>
                    <Grid size={{ xs: 6, sm: 3 }}>
                      <Typography variant="caption" color="text.secondary">
                        Protein: {version.nutritionalSummary.crudeProtein.toFixed(1)}%
                      </Typography>
                    </Grid>
                    <Grid size={{ xs: 6, sm: 3 }}>
                      <Typography variant="caption" color="text.secondary">
                        Energy: {version.nutritionalSummary.metabolizableEnergy.toFixed(1)} Mcal/kg
                      </Typography>
                    </Grid>
                    <Grid size={{ xs: 6, sm: 3 }}>
                      <Typography variant="caption" color="text.secondary">
                        Ingredients: {version.ingredients.length}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </Box>

              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleMenuOpen(e, version);
                }}
              >
                <MoreVertIcon />
              </IconButton>
            </Box>
          </Paper>
        ))}
      </Box>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem
          onClick={() => {
            if (selectedVersion) {
              handleRestoreVersion(selectedVersion);
            }
            handleMenuClose();
          }}
          disabled={selectedVersion?.id === history.versions[0].id}
        >
          <RestoreIcon sx={{ mr: 1 }} />
          Restore This Version
        </MenuItem>
      </Menu>

      {/* Comparison Dialog */}
      <Dialog
        open={compareDialogOpen}
        onClose={() => setCompareDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Version Comparison
          {comparisonResult && (
            <Typography variant="subtitle2" color="text.secondary">
              {comparisonResult.fromVersion} → {comparisonResult.toVersion}
            </Typography>
          )}
        </DialogTitle>
        <DialogContent>
          {comparisonResult && (
            <Box>
              {/* Summary */}
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid size={{ xs: 6, sm: 3 }}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center', py: 1 }}>
                      <Typography variant="h6" color="primary">
                        {comparisonResult.summary.ingredientsChanged}
                      </Typography>
                      <Typography variant="caption">
                        Ingredients Changed
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 6, sm: 3 }}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center', py: 1 }}>
                      <Typography variant="h6" color={comparisonResult.summary.nutritionChanged ? 'warning.main' : 'success.main'}>
                        {comparisonResult.summary.nutritionChanged ? 'Yes' : 'No'}
                      </Typography>
                      <Typography variant="caption">
                        Nutrition Changed
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 6, sm: 3 }}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center', py: 1 }}>
                      <Typography variant="h6" color={comparisonResult.summary.costChanged ? 'warning.main' : 'success.main'}>
                        {comparisonResult.summary.costChanged ? 'Yes' : 'No'}
                      </Typography>
                      <Typography variant="caption">
                        Cost Changed
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 6, sm: 3 }}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center', py: 1 }}>
                      <Typography variant="h6" color={comparisonResult.summary.optimizationChanged ? 'info.main' : 'success.main'}>
                        {comparisonResult.summary.optimizationChanged ? 'Yes' : 'No'}
                      </Typography>
                      <Typography variant="caption">
                        Optimization Changed
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Detailed Changes */}
              <Typography variant="h6" gutterBottom>
                Detailed Changes
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Field</TableCell>
                      <TableCell>Change Type</TableCell>
                      <TableCell>Old Value</TableCell>
                      <TableCell>New Value</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {comparisonResult.changes.map((change, index) => (
                      <TableRow key={index}>
                        <TableCell>{change.field}</TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {getChangeTypeIcon(change.changeType)}
                            {change.changeType}
                          </Box>
                        </TableCell>
                        <TableCell>
                          {change.oldValue !== null ? String(change.oldValue) : '-'}
                        </TableCell>
                        <TableCell>
                          {change.newValue !== null ? String(change.newValue) : '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCompareDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default HistoryViewer;
