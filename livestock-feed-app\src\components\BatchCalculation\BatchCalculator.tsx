import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Chip,
  Alert,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import {
  Calculate as CalculateIcon,
  GetApp as ExportIcon,
  TrendingUp as OptimizeIcon,
  Scale as ScaleIcon,
} from '@mui/icons-material';
import type { Ration, BatchCalculation } from '../../types';
import { batchCalculationService } from '../../services/batchCalculationService';
import { ExportDialog } from '../Export';

interface BatchCalculatorProps {
  ration: Ration;
  onBatchCalculated?: (calculation: BatchCalculation) => void;
}

const BatchCalculator: React.FC<BatchCalculatorProps> = ({
  ration,
  onBatchCalculated,
}) => {
  const [targetBatchSize, setTargetBatchSize] = useState<number>(100);
  const [batchCalculation, setBatchCalculation] = useState<BatchCalculation | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [recommendations, setRecommendations] = useState<any>(null);
  const [multipleBatches, setMultipleBatches] = useState<BatchCalculation[]>([]);
  const [comparisonSizes, setComparisonSizes] = useState<number[]>([50, 100, 200, 500]);

  // Calculate batch when target size changes
  useEffect(() => {
    if (targetBatchSize > 0) {
      calculateBatch();
    }
  }, [targetBatchSize, ration]);

  // Load recommendations
  useEffect(() => {
    loadRecommendations();
  }, [ration]);

  const calculateBatch = async () => {
    try {
      setLoading(true);
      setError(null);

      const calculation = await batchCalculationService.calculateBatch(ration, targetBatchSize);
      setBatchCalculation(calculation);
      
      if (onBatchCalculated) {
        onBatchCalculated(calculation);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to calculate batch');
      console.error('Batch calculation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadRecommendations = async () => {
    try {
      const recs = await batchCalculationService.getBatchSizeRecommendations(ration);
      setRecommendations(recs);
    } catch (err) {
      console.error('Error loading recommendations:', err);
    }
  };

  const calculateMultipleBatches = async () => {
    try {
      setLoading(true);
      const calculations = await batchCalculationService.calculateMultipleBatches(ration, comparisonSizes);
      setMultipleBatches(calculations);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to calculate multiple batches');
      console.error('Multiple batch calculation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleBatchSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(event.target.value);
    if (!isNaN(value) && value > 0) {
      setTargetBatchSize(value);
    }
  };

  const handleSliderChange = (event: Event, newValue: number | number[]) => {
    setTargetBatchSize(newValue as number);
  };

  const useRecommendedSize = () => {
    if (recommendations?.recommended) {
      setTargetBatchSize(recommendations.recommended);
    }
  };

  const formatCurrency = (amount: number) => `₹${amount.toFixed(2)}`;

  return (
    <Box>
      <Typography variant="h5" component="h2" gutterBottom>
        Batch Calculator
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Calculate scaled quantities and costs for different batch sizes
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Input Section */}
        <Grid size={{ xs: 12, md: 4 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Batch Configuration
            </Typography>

            <Box sx={{ mb: 3 }}>
              <TextField
                fullWidth
                label="Target Batch Size (kg)"
                type="number"
                value={targetBatchSize}
                onChange={handleBatchSizeChange}
                inputProps={{ min: 1, step: 1 }}
                sx={{ mb: 2 }}
              />

              <Typography variant="body2" color="text.secondary" gutterBottom>
                Adjust with slider:
              </Typography>
              <Slider
                value={targetBatchSize}
                onChange={handleSliderChange}
                min={10}
                max={1000}
                step={10}
                marks={[
                  { value: 50, label: '50kg' },
                  { value: 100, label: '100kg' },
                  { value: 500, label: '500kg' },
                  { value: 1000, label: '1000kg' },
                ]}
                valueLabelDisplay="auto"
              />
            </Box>

            {/* Recommendations */}
            {recommendations && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Recommendations
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={useRecommendedSize}
                  startIcon={<OptimizeIcon />}
                  sx={{ mb: 1 }}
                >
                  Use Recommended: {recommendations.recommended}kg
                </Button>
                <Box>
                  {recommendations.reasoning.map((reason: string, index: number) => (
                    <Typography key={index} variant="caption" display="block" color="text.secondary">
                      • {reason}
                    </Typography>
                  ))}
                </Box>
              </Box>
            )}

            <Button
              fullWidth
              variant="contained"
              startIcon={<CalculateIcon />}
              onClick={calculateBatch}
              disabled={loading}
            >
              {loading ? 'Calculating...' : 'Calculate Batch'}
            </Button>

            <Button
              fullWidth
              variant="outlined"
              startIcon={<ScaleIcon />}
              onClick={calculateMultipleBatches}
              disabled={loading}
              sx={{ mt: 1 }}
            >
              Compare Sizes
            </Button>
          </Paper>
        </Grid>

        {/* Results Section */}
        <Grid size={{ xs: 12, md: 8 }}>
          {batchCalculation && (
            <Paper sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">
                  Batch Calculation Results
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<ExportIcon />}
                  onClick={() => setExportDialogOpen(true)}
                >
                  Export
                </Button>
              </Box>

              {/* Summary Cards */}
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid size={{ xs: 6, sm: 3 }}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h5" color="primary">
                        {batchCalculation.targetBatchSize}kg
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Batch Size
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 6, sm: 3 }}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h5" color="success.main">
                        {formatCurrency(batchCalculation.totalCost)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Cost
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 6, sm: 3 }}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h5" color="warning.main">
                        {formatCurrency(batchCalculation.costPerKg)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Cost per kg
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 6, sm: 3 }}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h5" color="info.main">
                        {batchCalculation.scaledIngredients.length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Ingredients
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Scaled Ingredients Table */}
              <Typography variant="subtitle1" gutterBottom>
                Scaled Ingredients
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Ingredient</TableCell>
                      <TableCell align="right">Percentage</TableCell>
                      <TableCell align="right">Quantity (kg)</TableCell>
                      <TableCell align="right">Cost</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {batchCalculation.scaledIngredients.map((scaledIng, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">
                              {scaledIng.ingredient.name}
                            </Typography>
                            <Chip
                              label={scaledIng.ingredient.category}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          {scaledIng.originalPercentage.toFixed(1)}%
                        </TableCell>
                        <TableCell align="right">
                          {scaledIng.scaledQuantity.toFixed(2)}
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(scaledIng.scaledCost)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          )}

          {/* Multiple Batch Comparison */}
          {multipleBatches.length > 0 && (
            <Paper sx={{ p: 3, mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Batch Size Comparison
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Batch Size</TableCell>
                      <TableCell align="right">Total Cost</TableCell>
                      <TableCell align="right">Cost per kg</TableCell>
                      <TableCell align="right">Efficiency</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {multipleBatches.map((batch, index) => {
                      const minCostPerKg = Math.min(...multipleBatches.map(b => b.costPerKg));
                      const isOptimal = batch.costPerKg === minCostPerKg;
                      
                      return (
                        <TableRow key={index} sx={{ bgcolor: isOptimal ? 'success.50' : 'inherit' }}>
                          <TableCell>
                            {batch.targetBatchSize}kg
                            {isOptimal && (
                              <Chip label="Optimal" size="small" color="success" sx={{ ml: 1 }} />
                            )}
                          </TableCell>
                          <TableCell align="right">
                            {formatCurrency(batch.totalCost)}
                          </TableCell>
                          <TableCell align="right">
                            {formatCurrency(batch.costPerKg)}
                          </TableCell>
                          <TableCell align="right">
                            {((minCostPerKg / batch.costPerKg) * 100).toFixed(1)}%
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          )}
        </Grid>
      </Grid>

      {/* Export Dialog */}
      <ExportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        batchCalculation={batchCalculation || undefined}
        title="Export Batch Calculation"
      />
    </Box>
  );
};

export default BatchCalculator;
