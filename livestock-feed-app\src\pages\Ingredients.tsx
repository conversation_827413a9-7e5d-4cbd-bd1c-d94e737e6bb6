import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import {
  Add as AddIcon,
} from '@mui/icons-material';
import type { Ingredient, IngredientFormData } from '../types';
import { IngredientForm, IngredientDetail, IngredientList } from '../components/Ingredients';
import {
  getAllIngredients,
  getIngredientById,
  createIngredient,
  updateIngredient,
  deleteIngredient,
  getIngredientStats,
  type IngredientFilters,
} from '../services/ingredientService';

type ViewMode = 'list' | 'add' | 'edit' | 'detail';

interface NotificationState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
}

const Ingredients: React.FC = () => {
  // State management
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [selectedIngredient, setSelectedIngredient] = useState<Ingredient | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [ingredientToDelete, setIngredientToDelete] = useState<Ingredient | null>(null);
  const [notification, setNotification] = useState<NotificationState>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Pagination and filtering
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState<IngredientFilters>({});

  // Statistics
  const [stats, setStats] = useState({
    total: 0,
    categories: 0,
    averageCost: 0,
    averageProtein: 0,
    available: 0,
    unavailable: 0,
  });

  // Load ingredients data
  const loadIngredients = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await getAllIngredients(filters, { page: page + 1, limit: rowsPerPage });
      setIngredients(result.ingredients);
      setTotal(result.total);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load ingredients');
    } finally {
      setLoading(false);
    }
  };

  // Load statistics
  const loadStats = async () => {
    try {
      const statsData = await getIngredientStats();
      setStats(statsData);
    } catch (err) {
      console.error('Failed to load stats:', err);
    }
  };

  // Load data on component mount and when dependencies change
  useEffect(() => {
    loadIngredients();
  }, [page, rowsPerPage, filters]);

  useEffect(() => {
    loadStats();
  }, [ingredients]);

  // Notification helper
  const showNotification = (message: string, severity: NotificationState['severity'] = 'success') => {
    setNotification({ open: true, message, severity });
  };

  // Event handlers
  const handleAddIngredient = () => {
    setSelectedIngredient(null);
    setViewMode('add');
  };

  const handleViewIngredient = (ingredient: Ingredient) => {
    setSelectedIngredient(ingredient);
    setViewMode('detail');
  };

  const handleEditIngredient = (ingredient: Ingredient) => {
    setSelectedIngredient(ingredient);
    setViewMode('edit');
  };

  const handleDeleteIngredient = (ingredient: Ingredient) => {
    setIngredientToDelete(ingredient);
    setDeleteDialogOpen(true);
  };

  const handleBackToList = () => {
    setViewMode('list');
    setSelectedIngredient(null);
  };

  // Form submission handlers
  const handleFormSubmit = async (data: IngredientFormData) => {
    try {
      setLoading(true);
      if (viewMode === 'add') {
        await createIngredient(data);
        showNotification('Ingredient created successfully');
      } else if (viewMode === 'edit' && selectedIngredient) {
        await updateIngredient(selectedIngredient.id, data);
        showNotification('Ingredient updated successfully');
      }
      setViewMode('list');
      setSelectedIngredient(null);
      await loadIngredients();
      await loadStats();
    } catch (err) {
      showNotification(
        err instanceof Error ? err.message : 'Failed to save ingredient',
        'error'
      );
    } finally {
      setLoading(false);
    }
  };

  // Delete confirmation handler
  const confirmDelete = async () => {
    if (!ingredientToDelete) return;

    try {
      setLoading(true);
      await deleteIngredient(ingredientToDelete.id);
      showNotification('Ingredient deleted successfully');
      setDeleteDialogOpen(false);
      setIngredientToDelete(null);
      await loadIngredients();
      await loadStats();
    } catch (err) {
      showNotification(
        err instanceof Error ? err.message : 'Failed to delete ingredient',
        'error'
      );
    } finally {
      setLoading(false);
    }
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(0);
  };

  // Filter handlers
  const handleFiltersChange = (newFilters: IngredientFilters) => {
    setFilters(newFilters);
    setPage(0);
  };

  // Render different views based on current mode
  const renderContent = () => {
    switch (viewMode) {
      case 'add':
      case 'edit':
        return (
          <IngredientForm
            ingredient={selectedIngredient || undefined}
            onSubmit={handleFormSubmit}
            onCancel={handleBackToList}
            isLoading={loading}
          />
        );

      case 'detail':
        return selectedIngredient ? (
          <IngredientDetail
            ingredient={selectedIngredient}
            onEdit={() => setViewMode('edit')}
            onDelete={() => handleDeleteIngredient(selectedIngredient)}
            onBack={handleBackToList}
            isLoading={loading}
          />
        ) : null;

      case 'list':
      default:
        return (
          <>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
              <Box>
                <Typography variant="h4" component="h1" gutterBottom color="primary">
                  Feed Ingredients
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage feed ingredient database with nutritional values
                </Typography>
              </Box>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAddIngredient}
                disabled={loading}
              >
                Add Ingredient
              </Button>
            </Box>

            {/* Statistics Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <Card sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" color="primary">
                    {stats.total}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Ingredients
                  </Typography>
                </Card>
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <Card sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" color="secondary">
                    {stats.categories}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Categories
                  </Typography>
                </Card>
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <Card sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" color="primary">
                    ₹{stats.averageCost.toLocaleString('en-IN')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Avg Cost/ton
                  </Typography>
                </Card>
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <Card sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" color="secondary">
                    {stats.averageProtein}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Avg Protein
                  </Typography>
                </Card>
              </Grid>
            </Grid>

            {/* Ingredients List */}
            <IngredientList
              ingredients={ingredients}
              total={total}
              page={page}
              rowsPerPage={rowsPerPage}
              filters={filters}
              onPageChange={handlePageChange}
              onRowsPerPageChange={handleRowsPerPageChange}
              onFiltersChange={handleFiltersChange}
              onView={handleViewIngredient}
              onEdit={handleEditIngredient}
              onDelete={handleDeleteIngredient}
              isLoading={loading}
              error={error}
            />
          </>
        );
    }
  };

  return (
    <Box>
      {renderContent()}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the ingredient "{ingredientToDelete?.name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={confirmDelete} color="error" disabled={loading}>
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, open: false })}
      >
        <Alert
          onClose={() => setNotification({ ...notification, open: false })}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Ingredients;
