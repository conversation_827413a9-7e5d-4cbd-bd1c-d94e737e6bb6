import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  Box,
  Button,
  Paper,
  Typography,
  TextField,
  MenuItem,
  FormControlLabel,
  Switch,
  Alert,
  CircularProgress,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import { Save as SaveIcon, Cancel as CancelIcon } from '@mui/icons-material';
import type { Ingredient, IngredientFormData } from '../../types';
import { getCategoriesList } from '../../services/ingredientService';

// Validation schema
const ingredientSchema = yup.object({
  name: yup
    .string()
    .required('Ingredient name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must not exceed 100 characters'),
  category: yup
    .string()
    .required('Category is required'),
  dryMatter: yup
    .number()
    .required('Dry matter is required')
    .min(0, 'Dry matter must be positive')
    .max(100, 'Dry matter cannot exceed 100%'),
  crudeProtein: yup
    .number()
    .required('Crude protein is required')
    .min(0, 'Crude protein must be positive')
    .max(100, 'Crude protein cannot exceed 100%'),
  metabolizableEnergy: yup
    .number()
    .required('Metabolizable energy is required')
    .min(0, 'Energy must be positive')
    .max(20, 'Energy seems too high (max 20 MJ/kg)'),
  crudefiber: yup
    .number()
    .min(0, 'Crude fiber must be positive')
    .max(100, 'Crude fiber cannot exceed 100%')
    .nullable()
    .transform((value, originalValue) => originalValue === '' ? null : value),
  calcium: yup
    .number()
    .min(0, 'Calcium must be positive')
    .max(50, 'Calcium seems too high (max 50%)')
    .nullable()
    .transform((value, originalValue) => originalValue === '' ? null : value),
  phosphorus: yup
    .number()
    .min(0, 'Phosphorus must be positive')
    .max(20, 'Phosphorus seems too high (max 20%)')
    .nullable()
    .transform((value, originalValue) => originalValue === '' ? null : value),
  cost: yup
    .number()
    .required('Cost is required')
    .min(0, 'Cost must be positive'),
  availability: yup
    .boolean()
    .required(),
});

interface IngredientFormProps {
  ingredient?: Ingredient;
  onSubmit: (data: IngredientFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const IngredientForm: React.FC<IngredientFormProps> = ({
  ingredient,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const [categories, setCategories] = useState<string[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [categoriesError, setCategoriesError] = useState<string | null>(null);

  const isEditing = !!ingredient;

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<IngredientFormData>({
    resolver: yupResolver(ingredientSchema),
    defaultValues: {
      name: ingredient?.name || '',
      category: ingredient?.category || '',
      dryMatter: ingredient?.dryMatter || 0,
      crudeProtein: ingredient?.crudeProtein || 0,
      metabolizableEnergy: ingredient?.metabolizableEnergy || 0,
      crudefiber: ingredient?.crudefiber || undefined,
      calcium: ingredient?.calcium || undefined,
      phosphorus: ingredient?.phosphorus || undefined,
      cost: ingredient?.cost || 0,
      availability: ingredient?.availability ?? true,
    },
  });

  // Load categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setCategoriesLoading(true);
        setCategoriesError(null);
        const categoriesList = await getCategoriesList();
        setCategories(categoriesList);
      } catch (error) {
        console.error('Failed to load categories:', error);
        setCategoriesError('Failed to load categories');
      } finally {
        setCategoriesLoading(false);
      }
    };

    loadCategories();
  }, []);

  const handleFormSubmit = async (data: IngredientFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleReset = () => {
    reset();
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        {isEditing ? 'Edit Ingredient' : 'Add New Ingredient'}
      </Typography>

      {categoriesError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {categoriesError}
        </Alert>
      )}

      <Box component="form" onSubmit={handleSubmit(handleFormSubmit)}>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="subtitle1" gutterBottom sx={{ mt: 2, mb: 1 }}>
              Basic Information
            </Typography>
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Ingredient Name"
                  fullWidth
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  disabled={isSubmitting || isLoading}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <Controller
              name="category"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  select
                  label="Category"
                  fullWidth
                  error={!!errors.category}
                  helperText={errors.category?.message}
                  disabled={isSubmitting || isLoading || categoriesLoading}
                >
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>

          {/* Nutritional Information */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="subtitle1" gutterBottom sx={{ mt: 2, mb: 1 }}>
              Nutritional Information
            </Typography>
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <Controller
              name="dryMatter"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Dry Matter (%)"
                  type="number"
                  fullWidth
                  error={!!errors.dryMatter}
                  helperText={errors.dryMatter?.message}
                  disabled={isSubmitting || isLoading}
                  inputProps={{ step: 0.1, min: 0, max: 100 }}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <Controller
              name="crudeProtein"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Crude Protein (%)"
                  type="number"
                  fullWidth
                  error={!!errors.crudeProtein}
                  helperText={errors.crudeProtein?.message}
                  disabled={isSubmitting || isLoading}
                  inputProps={{ step: 0.1, min: 0, max: 100 }}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <Controller
              name="metabolizableEnergy"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Metabolizable Energy (MJ/kg)"
                  type="number"
                  fullWidth
                  error={!!errors.metabolizableEnergy}
                  helperText={errors.metabolizableEnergy?.message}
                  disabled={isSubmitting || isLoading}
                  inputProps={{ step: 0.1, min: 0, max: 20 }}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <Controller
              name="crudefiber"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Crude Fiber (%) - Optional"
                  type="number"
                  fullWidth
                  error={!!errors.crudefiber}
                  helperText={errors.crudefiber?.message}
                  disabled={isSubmitting || isLoading}
                  inputProps={{ step: 0.1, min: 0, max: 100 }}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <Controller
              name="calcium"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Calcium (%) - Optional"
                  type="number"
                  fullWidth
                  error={!!errors.calcium}
                  helperText={errors.calcium?.message}
                  disabled={isSubmitting || isLoading}
                  inputProps={{ step: 0.01, min: 0, max: 50 }}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <Controller
              name="phosphorus"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Phosphorus (%) - Optional"
                  type="number"
                  fullWidth
                  error={!!errors.phosphorus}
                  helperText={errors.phosphorus?.message}
                  disabled={isSubmitting || isLoading}
                  inputProps={{ step: 0.01, min: 0, max: 20 }}
                />
              )}
            />
          </Grid>

          {/* Cost and Availability */}
          <Grid size={{ xs: 12 }}>
            <Typography variant="subtitle1" gutterBottom sx={{ mt: 2, mb: 1 }}>
              Cost and Availability
            </Typography>
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <Controller
              name="cost"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Cost per Ton (₹)"
                  type="number"
                  fullWidth
                  error={!!errors.cost}
                  helperText={errors.cost?.message}
                  disabled={isSubmitting || isLoading}
                  inputProps={{ step: 0.01, min: 0 }}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6 }}>
            <Controller
              name="availability"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Switch
                      checked={field.value}
                      onChange={field.onChange}
                      disabled={isSubmitting || isLoading}
                    />
                  }
                  label="Available"
                  sx={{ mt: 1 }}
                />
              )}
            />
          </Grid>

          {/* Form Actions */}
          <Grid size={{ xs: 12 }}>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
              <Button
                variant="outlined"
                onClick={onCancel}
                disabled={isSubmitting || isLoading}
                startIcon={<CancelIcon />}
              >
                Cancel
              </Button>
              <Button
                variant="outlined"
                onClick={handleReset}
                disabled={isSubmitting || isLoading}
              >
                Reset
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={isSubmitting || isLoading}
                startIcon={isSubmitting || isLoading ? <CircularProgress size={20} /> : <SaveIcon />}
              >
                {isSubmitting || isLoading ? 'Saving...' : isEditing ? 'Update' : 'Create'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default IngredientForm;
