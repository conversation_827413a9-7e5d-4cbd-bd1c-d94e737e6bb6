import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  Box,
  Paper,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Switch,
  FormControlLabel,
  Alert,
  Autocomplete,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import type { RationTemplateFormData, TemplateIngredient, Ingredient } from '../../types';
import { rationTemplateService } from '../../services/rationTemplateService';
import { ingredientService } from '../../services/ingredientService';

// Validation schema
const templateSchema = yup.object({
  name: yup.string().required('Template name is required').min(3, 'Name must be at least 3 characters'),
  description: yup.string().max(500, 'Description must be less than 500 characters'),
  category: yup.string().required('Category is required'),
  animalType: yup.string().required('Animal type is required'),
  purpose: yup.string().required('Purpose is required'),
  ingredients: yup.array().of(
    yup.object({
      ingredientId: yup.string().required('Ingredient is required'),
      minPercentage: yup.number().min(0, 'Minimum percentage must be positive').max(100, 'Cannot exceed 100%'),
      maxPercentage: yup.number().min(0, 'Maximum percentage must be positive').max(100, 'Cannot exceed 100%'),
      recommendedPercentage: yup.number().min(0, 'Recommended percentage must be positive').max(100, 'Cannot exceed 100%'),
      isRequired: yup.boolean(),
    })
  ).min(1, 'At least one ingredient is required'),
  nutritionalTargets: yup.object({
    crudeProtein: yup.number().min(0, 'Protein must be positive').max(100, 'Cannot exceed 100%'),
    metabolizableEnergy: yup.number().min(0, 'Energy must be positive').max(10, 'Energy seems too high'),
    crudefiber: yup.number().min(0, 'Fiber must be positive').max(100, 'Cannot exceed 100%'),
    calcium: yup.number().min(0, 'Calcium must be positive').max(10, 'Calcium seems too high'),
    phosphorus: yup.number().min(0, 'Phosphorus must be positive').max(10, 'Phosphorus seems too high'),
  }),
  tags: yup.array().of(yup.string()),
  isPublic: yup.boolean(),
});

interface TemplateFormProps {
  templateId?: string;
  onSave: (template: any) => void;
  onCancel: () => void;
}

const TemplateForm: React.FC<TemplateFormProps> = ({
  templateId,
  onSave,
  onCancel,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [availableTags, setAvailableTags] = useState<string[]>([
    'high-protein', 'low-cost', 'energy-dense', 'fiber-rich', 'starter', 'grower', 'finisher',
    'lactation', 'maintenance', 'organic', 'conventional', 'premium'
  ]);

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<RationTemplateFormData>({
    resolver: yupResolver(templateSchema),
    defaultValues: {
      name: '',
      description: '',
      category: '',
      animalType: '',
      purpose: '',
      ingredients: [],
      nutritionalTargets: {
        crudeProtein: 16,
        metabolizableEnergy: 2.7,
        crudefiber: 15,
        calcium: 0.8,
        phosphorus: 0.45,
      },
      tags: [],
      isPublic: false,
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'ingredients',
  });

  // Load ingredients and template data
  useEffect(() => {
    loadIngredients();
    if (templateId) {
      loadTemplate();
    }
  }, [templateId]);

  const loadIngredients = async () => {
    try {
      const response = await ingredientService.getAllIngredients(1, 100);
      setIngredients(response.data);
    } catch (err) {
      console.error('Error loading ingredients:', err);
    }
  };

  const loadTemplate = async () => {
    if (!templateId) return;

    try {
      setLoading(true);
      const template = await rationTemplateService.getTemplateById(templateId);
      if (template) {
        reset({
          name: template.name,
          description: template.description || '',
          category: template.category,
          animalType: template.animalType,
          purpose: template.purpose,
          ingredients: template.ingredients,
          nutritionalTargets: template.nutritionalTargets,
          tags: template.tags,
          isPublic: template.isPublic,
        });
      }
    } catch (err) {
      setError('Failed to load template');
      console.error('Error loading template:', err);
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: RationTemplateFormData) => {
    try {
      setLoading(true);
      setError(null);

      // Validate ingredient percentages
      const totalMinPercentage = data.ingredients.reduce((sum, ing) => sum + ing.minPercentage, 0);
      const totalMaxPercentage = data.ingredients.reduce((sum, ing) => sum + ing.maxPercentage, 0);

      if (totalMinPercentage > 100) {
        setError('Total minimum percentages cannot exceed 100%');
        return;
      }

      if (totalMaxPercentage < 100) {
        setError('Total maximum percentages should allow for 100% formulation');
        return;
      }

      // Validate recommended percentages
      for (const ing of data.ingredients) {
        if (ing.recommendedPercentage < ing.minPercentage || ing.recommendedPercentage > ing.maxPercentage) {
          setError(`Recommended percentage for ingredient must be between min and max values`);
          return;
        }
      }

      let savedTemplate;
      if (templateId) {
        savedTemplate = await rationTemplateService.updateTemplate(templateId, data);
      } else {
        savedTemplate = await rationTemplateService.createTemplate(data);
      }

      onSave(savedTemplate);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save template');
      console.error('Error saving template:', err);
    } finally {
      setLoading(false);
    }
  };

  const addIngredient = () => {
    append({
      ingredientId: '',
      minPercentage: 0,
      maxPercentage: 100,
      recommendedPercentage: 10,
      isRequired: false,
      notes: '',
    });
  };

  const removeIngredient = (index: number) => {
    remove(index);
  };

  const getIngredientName = (ingredientId: string) => {
    const ingredient = ingredients.find(ing => ing.id === ingredientId);
    return ingredient ? ingredient.name : 'Unknown Ingredient';
  };

  if (loading && templateId) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography>Loading template...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          {templateId ? 'Edit Template' : 'Create New Template'}
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid size={{ xs: 12 }}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Template Name"
                    error={!!errors.name}
                    helperText={errors.name?.message}
                  />
                )}
              />
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="category"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.category}>
                    <InputLabel>Category</InputLabel>
                    <Select {...field} label="Category">
                      <MenuItem value="Dairy">Dairy</MenuItem>
                      <MenuItem value="Beef">Beef</MenuItem>
                      <MenuItem value="Swine">Swine</MenuItem>
                      <MenuItem value="Poultry">Poultry</MenuItem>
                      <MenuItem value="Sheep">Sheep</MenuItem>
                      <MenuItem value="Goat">Goat</MenuItem>
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="animalType"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.animalType}>
                    <InputLabel>Animal Type</InputLabel>
                    <Select {...field} label="Animal Type">
                      <MenuItem value="cattle">Cattle</MenuItem>
                      <MenuItem value="swine">Swine</MenuItem>
                      <MenuItem value="poultry">Poultry</MenuItem>
                      <MenuItem value="sheep">Sheep</MenuItem>
                      <MenuItem value="goat">Goat</MenuItem>
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="purpose"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.purpose}>
                    <InputLabel>Purpose</InputLabel>
                    <Select {...field} label="Purpose">
                      <MenuItem value="starter">Starter</MenuItem>
                      <MenuItem value="grower">Grower</MenuItem>
                      <MenuItem value="finisher">Finisher</MenuItem>
                      <MenuItem value="lactation">Lactation</MenuItem>
                      <MenuItem value="maintenance">Maintenance</MenuItem>
                      <MenuItem value="breeding">Breeding</MenuItem>
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    multiline
                    rows={3}
                    label="Description"
                    error={!!errors.description}
                    helperText={errors.description?.message}
                  />
                )}
              />
            </Grid>

            {/* Tags */}
            <Grid size={{ xs: 12 }}>
              <Controller
                name="tags"
                control={control}
                render={({ field }) => (
                  <Autocomplete
                    {...field}
                    multiple
                    freeSolo
                    options={availableTags}
                    value={field.value || []}
                    onChange={(_, newValue) => field.onChange(newValue)}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip variant="outlined" label={option} {...getTagProps({ index })} key={index} />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Tags"
                        placeholder="Add tags..."
                        helperText="Press Enter to add custom tags"
                      />
                    )}
                  />
                )}
              />
            </Grid>

            {/* Ingredients Section */}
            <Grid size={{ xs: 12 }}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Ingredients
              </Typography>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={addIngredient}
                sx={{ mb: 2 }}
              >
                Add Ingredient
              </Button>

              {fields.length > 0 && (
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Ingredient</TableCell>
                        <TableCell>Min %</TableCell>
                        <TableCell>Max %</TableCell>
                        <TableCell>Recommended %</TableCell>
                        <TableCell>Required</TableCell>
                        <TableCell>Notes</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {fields.map((field, index) => (
                        <TableRow key={field.id}>
                          <TableCell>
                            <Controller
                              name={`ingredients.${index}.ingredientId`}
                              control={control}
                              render={({ field: ingredientField }) => (
                                <FormControl fullWidth size="small">
                                  <Select
                                    {...ingredientField}
                                    displayEmpty
                                  >
                                    <MenuItem value="">Select ingredient...</MenuItem>
                                    {ingredients.map(ingredient => (
                                      <MenuItem key={ingredient.id} value={ingredient.id}>
                                        {ingredient.name}
                                      </MenuItem>
                                    ))}
                                  </Select>
                                </FormControl>
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <Controller
                              name={`ingredients.${index}.minPercentage`}
                              control={control}
                              render={({ field: minField }) => (
                                <TextField
                                  {...minField}
                                  type="number"
                                  size="small"
                                  inputProps={{ min: 0, max: 100, step: 0.1 }}
                                  sx={{ width: 80 }}
                                />
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <Controller
                              name={`ingredients.${index}.maxPercentage`}
                              control={control}
                              render={({ field: maxField }) => (
                                <TextField
                                  {...maxField}
                                  type="number"
                                  size="small"
                                  inputProps={{ min: 0, max: 100, step: 0.1 }}
                                  sx={{ width: 80 }}
                                />
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <Controller
                              name={`ingredients.${index}.recommendedPercentage`}
                              control={control}
                              render={({ field: recField }) => (
                                <TextField
                                  {...recField}
                                  type="number"
                                  size="small"
                                  inputProps={{ min: 0, max: 100, step: 0.1 }}
                                  sx={{ width: 80 }}
                                />
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <Controller
                              name={`ingredients.${index}.isRequired`}
                              control={control}
                              render={({ field: reqField }) => (
                                <Switch
                                  {...reqField}
                                  checked={reqField.value}
                                  size="small"
                                />
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <Controller
                              name={`ingredients.${index}.notes`}
                              control={control}
                              render={({ field: notesField }) => (
                                <TextField
                                  {...notesField}
                                  size="small"
                                  placeholder="Optional notes..."
                                  sx={{ width: 120 }}
                                />
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => removeIngredient(index)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Grid>

            {/* Nutritional Targets */}
            <Grid size={{ xs: 12 }}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Nutritional Targets
              </Typography>
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="nutritionalTargets.crudeProtein"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label="Crude Protein (%)"
                    inputProps={{ min: 0, max: 100, step: 0.1 }}
                  />
                )}
              />
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="nutritionalTargets.metabolizableEnergy"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label="Metabolizable Energy (Mcal/kg)"
                    inputProps={{ min: 0, max: 10, step: 0.1 }}
                  />
                )}
              />
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="nutritionalTargets.crudefiber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label="Crude Fiber (%)"
                    inputProps={{ min: 0, max: 100, step: 0.1 }}
                  />
                )}
              />
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="nutritionalTargets.calcium"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label="Calcium (%)"
                    inputProps={{ min: 0, max: 10, step: 0.01 }}
                  />
                )}
              />
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Controller
                name="nutritionalTargets.phosphorus"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label="Phosphorus (%)"
                    inputProps={{ min: 0, max: 10, step: 0.01 }}
                  />
                )}
              />
            </Grid>

            {/* Public/Private */}
            <Grid size={{ xs: 12 }}>
              <Controller
                name="isPublic"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Switch {...field} checked={field.value} />}
                    label="Make this template public (visible to all users)"
                  />
                )}
              />
            </Grid>
          </Grid>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 2, mt: 4 }}>
            <Button
              type="submit"
              variant="contained"
              startIcon={<SaveIcon />}
              disabled={loading}
            >
              {loading ? 'Saving...' : templateId ? 'Update Template' : 'Create Template'}
            </Button>
            <Button
              variant="outlined"
              startIcon={<CancelIcon />}
              onClick={onCancel}
              disabled={loading}
            >
              Cancel
            </Button>
          </Box>
        </form>
      </Paper>
    </Box>
  );
};

export default TemplateForm;
