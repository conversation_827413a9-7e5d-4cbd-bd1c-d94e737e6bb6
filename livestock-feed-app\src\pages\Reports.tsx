import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Card,
  CardContent,
  CardActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import {
  GetApp as DownloadIcon,
  Assessment as AssessmentIcon,
  PictureAsPdf as PdfIcon,
  TableChart as ExcelIcon,
  BarChart as ChartIcon,
  TrendingUp as TrendingUpIcon,
  MonetizationOn as CostIcon,
  Science as NutritionIcon,
} from '@mui/icons-material';

const Reports: React.FC = () => {
  const reportTypes = [
    {
      title: 'Nutritional Analysis Report',
      description: 'Detailed breakdown of nutritional content in formulated rations',
      icon: <NutritionIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
      features: ['Nutrient composition', 'Deficiency analysis', 'Requirement comparison'],
    },
    {
      title: 'Cost Analysis Report',
      description: 'Cost breakdown and optimization suggestions for feed formulations',
      icon: <CostIcon sx={{ fontSize: 40, color: 'secondary.main' }} />,
      features: ['Ingredient costs', 'Total formulation cost', 'Cost per nutrient'],
    },
    {
      title: 'Performance Tracking',
      description: 'Track animal performance and feed efficiency over time',
      icon: <TrendingUpIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
      features: ['Growth rates', 'Feed conversion ratio', 'Performance trends'],
    },
    {
      title: 'Comparative Analysis',
      description: 'Compare different ration formulations side by side',
      icon: <ChartIcon sx={{ fontSize: 40, color: 'secondary.main' }} />,
      features: ['Ration comparison', 'Cost-benefit analysis', 'Nutritional differences'],
    },
  ];

  const recentReports = [
    {
      name: 'Dairy Cow Nutrition Report - January 2024',
      type: 'Nutritional Analysis',
      date: '2024-01-15',
      format: 'PDF',
    },
    {
      name: 'Feed Cost Analysis - Q4 2023',
      type: 'Cost Analysis',
      date: '2024-01-10',
      format: 'Excel',
    },
    {
      name: 'Broiler Performance Report',
      type: 'Performance Tracking',
      date: '2024-01-08',
      format: 'PDF',
    },
  ];

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom color="primary">
            Reports & Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Generate detailed nutritional and cost reports
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AssessmentIcon />}
          onClick={() => console.log('Generate new report')}
        >
          Generate Report
        </Button>
      </Box>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {reportTypes.map((report, index) => (
          <Grid size={{ xs: 12, sm: 6, md: 6 }} key={index}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                },
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {report.icon}
                  <Typography variant="h6" component="h3" sx={{ ml: 2 }}>
                    {report.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {report.description}
                </Typography>
                <List dense>
                  {report.features.map((feature, idx) => (
                    <ListItem key={idx} sx={{ py: 0 }}>
                      <ListItemIcon sx={{ minWidth: 20 }}>
                        <Box
                          sx={{
                            width: 6,
                            height: 6,
                            borderRadius: '50%',
                            backgroundColor: 'primary.main',
                          }}
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={feature}
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
              <CardActions>
                <Button size="small" variant="outlined">
                  Generate
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 8 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Reports
            </Typography>
            {recentReports.length > 0 ? (
              <List>
                {recentReports.map((report, index) => (
                  <ListItem
                    key={index}
                    sx={{
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 1,
                      mb: 1,
                    }}
                  >
                    <ListItemIcon>
                      {report.format === 'PDF' ? (
                        <PdfIcon color="error" />
                      ) : (
                        <ExcelIcon color="success" />
                      )}
                    </ListItemIcon>
                    <ListItemText
                      primary={report.name}
                      secondary={`${report.type} • ${report.date}`}
                    />
                    <Button
                      size="small"
                      startIcon={<DownloadIcon />}
                      onClick={() => console.log(`Download ${report.name}`)}
                    >
                      Download
                    </Button>
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No reports generated yet. Create your first report using the options above.
              </Typography>
            )}
          </Paper>
        </Grid>

        <Grid size={{ xs: 12, md: 4 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Export Options
            </Typography>
            <Grid container spacing={2}>
              <Grid size={{ xs: 6 }}>
                <Card sx={{ textAlign: 'center', p: 2 }}>
                  <PdfIcon sx={{ fontSize: 30, color: 'error.main', mb: 1 }} />
                  <Typography variant="body2">PDF</Typography>
                </Card>
              </Grid>
              <Grid size={{ xs: 6 }}>
                <Card sx={{ textAlign: 'center', p: 2 }}>
                  <ExcelIcon sx={{ fontSize: 30, color: 'success.main', mb: 1 }} />
                  <Typography variant="body2">Excel</Typography>
                </Card>
              </Grid>
            </Grid>
          </Paper>

          <Paper sx={{ p: 3, mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Report Statistics
            </Typography>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {recentReports.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Reports Generated
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      <Box sx={{ mt: 4 }}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Report Features
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            This section will include:
          </Typography>
          <ul>
            <li>Automated report generation</li>
            <li>Customizable report templates</li>
            <li>PDF and Excel export options</li>
            <li>Scheduled report delivery</li>
            <li>Interactive charts and graphs</li>
            <li>Historical data comparison</li>
          </ul>
        </Paper>
      </Box>
    </Box>
  );
};

export default Reports;
